generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String            @id @default(uuid())
  email            String            @unique
  name             String
  password         String
  role             Role              @default(USER)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  active           Boolean           @default(true)
  commandHistory   CommandHistory[]
  commandTemplates CommandTemplate[]
  servers          Server[]
  serverAccess     ServerUser[]
  serverGroups     ServerGroup[]
}

model Server {
  id             String              @id @default(uuid())
  name           String
  host           String
  port           Int                 @default(22)
  username       String
  password       String?
  privateKey     String?
  os             OS                  @default(LINUX)
  deviceType     DeviceType          @default(GENERIC)
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  userId         String
  commands       Command[]
  commandHistory CommandHistory[]
  user           User                @relation(fields: [userId], references: [id])
  userAccess     ServerUser[]
  groupMembers   ServerGroupMember[]
}

model ServerUser {
  id        String   @id @default(uuid())
  userId    String
  serverId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  server    Server   @relation(fields: [serverId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, serverId])
}

model Command {
  id                String           @id @default(uuid())
  name              String
  command           String
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  serverId          String
  description       String?
  order             Int              @default(0)
  version           Int              @default(1)
  isLatest          Boolean          @default(true)
  previousVersionId String?
  previousVersion   Command?         @relation("CommandVersions", fields: [previousVersionId], references: [id])
  nextVersions      Command[]        @relation("CommandVersions")
  server            Server           @relation(fields: [serverId], references: [id], onDelete: Cascade)
  commandHistory    CommandHistory[]

  @@index([serverId, isLatest])
}

model CommandHistory {
  id           String    @id @default(uuid())
  userId       String
  serverId     String
  commandId    String?
  commandName  String?
  commandText  String?
  result       String?
  status       Int       @default(0)
  executedAt   DateTime  @default(now())
  createdAt    DateTime  @default(now())
  command      Command?  @relation(fields: [commandId], references: [id], onDelete: SetNull)
  server       Server    @relation(fields: [serverId], references: [id], onDelete: Cascade)
  user         User      @relation(fields: [userId], references: [id])
}

model CommandTemplate {
  id          String                @id @default(uuid())
  name        String
  description String?
  isPublic    Boolean               @default(false)
  userId      String
  createdAt   DateTime              @default(now())
  updatedAt   DateTime              @updatedAt
  user        User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  commands    CommandTemplateItem[]
}

model CommandTemplateItem {
  id          String          @id @default(uuid())
  name        String
  command     String
  description String?
  templateId  String
  order       Int             @default(0)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  template    CommandTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
}

enum Role {
  ADMIN
  USER
}

enum OS {
  LINUX
  WINDOWS
}

enum DeviceType {
  NOKIA
  HUAWEI
  MIKROTIK
  DMOS
  GENERIC
}

model ServerGroup {
  id          String              @id @default(uuid())
  name        String
  description String?
  color       String?             @default("#3B82F6")
  userId      String
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  user        User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  members     ServerGroupMember[]

  @@unique([userId, name])
}

model ServerGroupMember {
  id        String      @id @default(uuid())
  groupId   String
  serverId  String
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  group     ServerGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  server    Server      @relation(fields: [serverId], references: [id], onDelete: Cascade)

  @@unique([groupId, serverId])
}
