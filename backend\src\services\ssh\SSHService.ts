import { SSHServer, CommandResult } from '../../types/server';
import { Logger } from '../../utils/logger';
import { ConnectionManager } from './connectionManager';
import { Device } from './deviceDetector';
import { ICommandExecutor, makeExecutor } from './executors';

/**
 * Serviço SSH principal que orquestra a conexão e execução de comandos
 */
export class SSHService {
  private connectionManager: ConnectionManager;
  private executor: ICommandExecutor | null = null;
  private deviceType: Device = Device.GENERIC;
  private keepaliveTimer: NodeJS.Timeout | null = null;
  private keepaliveInterval = 30000; // 30 segundos para keepalive manual

  constructor() {
    this.connectionManager = new ConnectionManager();
  }

  /**
   * Conecta ao servidor SSH
   * @param server Informações do servidor
   */
  async connect(server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<void> {
    try {
      // Limpar qualquer keepalive anterior
      this.stopKeepAlive();

      // Usar o deviceType do servidor ou GENERIC como fallback
      this.deviceType = (server.deviceType as Device) || Device.GENERIC;

      // Conectar ao servidor
      await this.connectionManager.connect(server);

      // Criar o executor apropriado
      this.executor = makeExecutor(this.deviceType, this.connectionManager.getSSH());

      Logger.log(`Conexão estabelecida com ${server.host} (${this.deviceType})`);

      // Iniciar keepalive manual para Mikrotik
      if (this.deviceType === Device.MIKROTIK) {
        this.startKeepAlive();
      }
    } catch (error) {
      Logger.error('Erro ao conectar ao servidor:', error);
      throw error;
    }
  }

  /**
   * Inicia o mecanismo de keepalive manual
   * Envia um comando simples periodicamente para manter a conexão ativa
   */
  private startKeepAlive(): void {
    // Limpar qualquer timer existente
    this.stopKeepAlive();

    Logger.log(`Iniciando keepalive manual para Mikrotik a cada ${this.keepaliveInterval/1000} segundos`);

    // Criar novo timer
    this.keepaliveTimer = setInterval(async () => {
      try {
        if (this.connectionManager.isConnectedAndReady() && this.executor) {
          Logger.log('Enviando comando de keepalive para Mikrotik');

          // Usar um comando simples que não cause sobrecarga no dispositivo
          const keepaliveCommand = '/system identity print';

          // Executar o comando diretamente no executor para evitar recursão
          await this.executor.executeCommand(keepaliveCommand);

          Logger.log('Keepalive para Mikrotik enviado com sucesso');
        } else {
          Logger.warn('Não foi possível enviar keepalive: conexão não está pronta');
          this.stopKeepAlive();
        }
      } catch (error) {
        Logger.error('Erro ao enviar keepalive para Mikrotik:', error);

        // Se ocorrer um erro no keepalive, parar o timer para evitar erros em cascata
        this.stopKeepAlive();

        // Não tentamos reconectar aqui para evitar loops infinitos
        // A reconexão será tratada na próxima tentativa de executar um comando
      }
    }, this.keepaliveInterval);
  }

  /**
   * Para o mecanismo de keepalive manual
   */
  private stopKeepAlive(): void {
    if (this.keepaliveTimer) {
      Logger.log('Parando keepalive manual para Mikrotik');
      clearInterval(this.keepaliveTimer);
      this.keepaliveTimer = null;
    }
  }

  /**
   * Executa um comando SSH
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      // Verificar se está conectado
      if (!this.connectionManager.isConnectedAndReady()) {
        const lastServer = this.connectionManager.getLastServer();
        if (lastServer) {
          Logger.log('Não está conectado. Tentando reconectar antes de executar o comando...');
          await this.connect(lastServer);
        } else {
          throw new Error('Não está conectado e não há informações do último servidor para reconectar');
        }
      }

      // Verificar se o executor foi criado
      if (!this.executor) {
        throw new Error('Executor não inicializado. Conecte-se a um servidor primeiro.');
      }

      // Executar o comando usando o executor apropriado
      try {
        return await this.executor.executeCommand(command);
      } catch (execError) {
        // Verificar se é um erro que requer reconexão
        const errorMessage = execError instanceof Error ? execError.message : String(execError);
        if (errorMessage.includes('RECONNECT_NEEDED') ||
            errorMessage.includes('keepalive') ||
            errorMessage.includes('timeout') ||
            errorMessage.includes('timed out')) {

          Logger.log('Detectada necessidade de reconexão. Tentando reconectar...');

          // Tentar reconectar
          const lastServer = this.connectionManager.getLastServer();
          if (lastServer) {
            // Estratégia de reconexão específica para Mikrotik
            if (this.deviceType === Device.MIKROTIK) {
              Logger.log('Aplicando estratégia de reconexão específica para Mikrotik');

              // Parar keepalive e forçar desconexão primeiro
              await this.disconnect();

              // Aguardar um momento maior antes de reconectar para Mikrotik
              Logger.log('Aguardando 8 segundos antes de reconectar ao Mikrotik...');
              await new Promise(resolve => setTimeout(resolve, 8000));

              // Tentar reconectar até 3 vezes com intervalos crescentes
              let reconnected = false;
              let attempts = 0;
              const maxAttempts = 3;

              while (!reconnected && attempts < maxAttempts) {
                attempts++;
                try {
                  Logger.log(`Tentativa de reconexão ${attempts}/${maxAttempts} para Mikrotik`);
                  await this.connect(lastServer);
                  reconnected = true;
                  Logger.log('Reconectado com sucesso ao Mikrotik');

                  // Aguardar um momento após a reconexão para estabilizar
                  Logger.log('Aguardando 2 segundos para estabilizar a conexão...');
                  await new Promise(resolve => setTimeout(resolve, 2000));
                } catch (reconnectError) {
                  Logger.error(`Falha na tentativa ${attempts} de reconexão ao Mikrotik:`, reconnectError);
                  if (attempts < maxAttempts) {
                    const delay = 5000 * attempts; // Atraso crescente: 5s, 10s, 15s
                    Logger.log(`Aguardando ${delay/1000}s antes da próxima tentativa...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                  }
                }
              }

              if (!reconnected) {
                throw new Error(`Falha ao reconectar ao Mikrotik após ${maxAttempts} tentativas`);
              }

              // Tentar executar o comando novamente com timeout aumentado
              Logger.log('Reconectado com sucesso. Tentando executar o comando novamente no Mikrotik...');
              return await this.executor.executeCommand(command);
            } else {
              // Estratégia padrão para outros dispositivos
              // Forçar desconexão primeiro
              await this.disconnect();

              // Aguardar um momento antes de reconectar
              Logger.log('Aguardando 3 segundos antes de reconectar...');
              await new Promise(resolve => setTimeout(resolve, 3000));

              // Reconectar
              await this.connect(lastServer);

              // Aguardar um momento após a reconexão para estabilizar
              Logger.log('Aguardando 1 segundo para estabilizar a conexão...');
              await new Promise(resolve => setTimeout(resolve, 1000));

              // Tentar executar o comando novamente
              Logger.log('Reconectado com sucesso. Tentando executar o comando novamente...');
              return await this.executor.executeCommand(command);
            }
          } else {
            throw new Error('Não foi possível reconectar: informações do servidor não disponíveis');
          }
        }

        // Se não for um erro de reconexão, propagar o erro original
        throw execError;
      }
    } catch (error) {
      Logger.error('Erro ao executar comando:', error);
      throw new Error(`Falha ao executar comando: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Desconecta do servidor SSH
   */
  async disconnect(): Promise<void> {
    try {
      // Parar o keepalive antes de desconectar
      this.stopKeepAlive();

      // Fechar a conexão API do Mikrotik se estiver usando
      if (this.deviceType === Device.MIKROTIK && this.executor) {
        try {
          // Verificar se o executor é um MikrotikExecutor e tem o método closeApi
          const mikrotikExecutor = this.executor as any;
          if (mikrotikExecutor.closeApi && typeof mikrotikExecutor.closeApi === 'function') {
            Logger.log('Fechando conexão API Mikrotik antes de desconectar SSH');
            await mikrotikExecutor.closeApi();
          }
        } catch (apiError) {
          Logger.error('Erro ao fechar conexão API Mikrotik:', apiError);
          // Continuar com a desconexão SSH mesmo se a API falhar
        }
      }

      await this.connectionManager.disconnect();
      this.executor = null;
      Logger.log('Desconectado do servidor SSH');
    } catch (error) {
      Logger.error('Erro ao desconectar do servidor SSH:', error);
      throw error;
    }
  }

  /**
   * Verifica se está conectado e pronto para executar comandos
   */
  isConnectedAndReady(): boolean {
    return this.connectionManager.isConnectedAndReady();
  }
}
